import { randomUUID } from 'crypto'
import { WiseDate } from '@wisemen/wise-date'
import { ClientType } from '../../client/client-type.js'
import { BookingStatus } from '../enums/booking-status.js'
import { UserUuid } from '../../../../app/users/entities/user.uuid.js'
import { generateUuid } from '../../../../utils/types/uuid.js'
import { Booking } from './booking.entity.js'
import { BookingUuid } from './booking.uuid.js'

export class BookingEntityBuilder {
  private readonly booking: Booking

  constructor () {
    this.booking = new Booking()
    this.booking.uuid = generateUuid()
    this.booking.createdAt = new Date()
    this.booking.updatedAt = new Date()
    this.booking.clientUuid = randomUUID()
    this.booking.clientType = ClientType.ORGANIZATION
    this.booking.status = BookingStatus.ACTIVE
    this.booking.isRecurring = false
    this.booking.startDate = null
    this.booking.endDate = null
    this.booking.iterationIntervalWeeks = null
    this.booking.remarksForDriver = null
    this.booking.remarksForPlanner = null
    this.booking.contractUuid = randomUUID()
    this.booking.careUserUuid = randomUUID()
    this.booking.createdByUserUuid = generateUuid()
    this.booking.updatedByUserUuid = generateUuid()
  }

  withUuid (uuid: BookingUuid): this {
    this.booking.uuid = uuid
    return this
  }

  withCreatedAt (createdAt: Date): this {
    this.booking.createdAt = createdAt
    return this
  }

  withUpdatedAt (updatedAt: Date): this {
    this.booking.updatedAt = updatedAt
    return this
  }

  withClientUuid (clientUuid: string): this {
    this.booking.clientUuid = clientUuid
    return this
  }

  withClientType (clientType: ClientType): this {
    this.booking.clientType = clientType
    return this
  }

  withStatus (status: BookingStatus): this {
    this.booking.status = status
    return this
  }

  withIsRecurring (isRecurring: boolean): this {
    this.booking.isRecurring = isRecurring
    return this
  }

  withStartDate (startDate: WiseDate | null): this {
    this.booking.startDate = startDate
    return this
  }

  withEndDate (endDate: WiseDate | null): this {
    this.booking.endDate = endDate
    return this
  }

  withIterationIntervalWeeks (iterationIntervalWeeks: number | null): this {
    this.booking.iterationIntervalWeeks = iterationIntervalWeeks
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): this {
    this.booking.remarksForDriver = remarksForDriver
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): this {
    this.booking.remarksForPlanner = remarksForPlanner
    return this
  }

  withContractUuid (contractUuid: string): this {
    this.booking.contractUuid = contractUuid
    return this
  }

  withCareUserUuid (careUserUuid: string): this {
    this.booking.careUserUuid = careUserUuid
    return this
  }

  withCreatedByUserUuid (createdByUserUuid: UserUuid): this {
    this.booking.createdByUserUuid = createdByUserUuid
    return this
  }

  withUpdatedByUserUuid (updatedByUserUuid: UserUuid): this {
    this.booking.updatedByUserUuid = updatedByUserUuid
    return this
  }

  build (): Booking {
    return this.booking
  }
}

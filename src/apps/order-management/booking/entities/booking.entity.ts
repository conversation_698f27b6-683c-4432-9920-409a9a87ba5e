import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { WiseDate, WiseDateColumn } from '@wisemen/wise-date'
import { ClientType, ClientTypeColumn } from '../../client/client-type.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { Contract } from '../../contract/entities/contract.entity.js'
import { CareUser } from '../../care-user/entities/care-user.entity.js'
import { Client } from '../../client/client.js'
import { RequestedTransportOrder } from '../../requested-transport-order/requested-transport-order.entity.js'
import { BookingStatus } from '../enums/booking-status.js'

@Entity()
export class OrderRequest {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @Index()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'uuid' })
  clientUuid: string

  @ClientTypeColumn()
  clientType: ClientType

  client?: Relation<Client>

  @Column({ type: 'enum', enum: BookingStatus })
  status: BookingStatus

  @Column({ type: 'boolean', default: false })
  isRecurring: boolean

  @WiseDateColumn({ nullable: true })
  startDate: WiseDate | null

  @WiseDateColumn({ nullable: true })
  endDate: WiseDate | null

  @Column({ type: 'int', nullable: true })
  iterationIntervalWeeks: number | null

  @Column({ type: 'varchar', nullable: true })
  remarksForDriver: string | null

  @Column({ type: 'varchar', nullable: true })
  remarksForPlanner: string | null

  @Column({ type: 'uuid' })
  contractUuid: string

  @ManyToOne(() => Contract)
  @JoinColumn({ name: 'contract_uuid' })
  contract?: Relation<Contract>

  @Column({ type: 'uuid' })
  careUserUuid: string

  @ManyToOne(() => CareUser)
  @JoinColumn({ name: 'care_user_uuid' })
  careUser?: Relation<CareUser>

  @Column({ type: 'uuid' })
  createdByUserUuid: string

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_user_uuid' })
  createdByUser?: Relation<User>

  @Column({ type: 'uuid' })
  updatedByUserUuid: string

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by_user_uuid' })
  updatedByUser?: Relation<User>

  @OneToMany(() => RequestedTransportOrder,
    requestedTransportOrder => requestedTransportOrder.orderRequest
  )
  requestedTransportOrders?: Array<Relation<RequestedTransportOrder>>
}

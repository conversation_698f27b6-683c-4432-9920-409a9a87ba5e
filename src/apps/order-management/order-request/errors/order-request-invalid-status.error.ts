import { HttpStatus } from '@nestjs/common'
import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { ApiError } from '../../../../modules/exceptions/api-errors/api-error.js'
import { ApiErrorStatus } from '../../../../modules/exceptions/api-errors/api-error-status.decorator.js'
import { OrderRequestStatus } from '../enums/order-request-status.js'

export class OrderRequestInvalidStatusError extends ApiError {
  @ApiErrorCode('order_request_invalid_status')
  code: 'order_request_invalid_status'

  meta: {
    currentStatus: OrderRequestStatus
    requiredStatus: OrderRequestStatus
  }

  @ApiErrorStatus(HttpStatus.BAD_REQUEST)
  declare status: '400'

  constructor (currentStatus: OrderRequestStatus, requiredStatus: OrderRequestStatus) {
    super(`Order request has status '${currentStatus}' but requires status '${requiredStatus}' to be accepted`)
    this.code = 'order_request_invalid_status'
    this.status = '400'
    this.meta = { currentStatus, requiredStatus }
  }
}

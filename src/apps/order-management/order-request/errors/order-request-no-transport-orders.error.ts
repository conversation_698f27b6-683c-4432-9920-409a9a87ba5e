import { HttpStatus } from '@nestjs/common'
import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { ApiError } from '../../../../modules/exceptions/api-errors/api-error.js'
import { ApiErrorStatus } from '../../../../modules/exceptions/api-errors/api-error-status.decorator.js'

export class OrderRequestNoTransportOrdersError extends ApiError {
  @ApiErrorCode('order_request_no_transport_orders')
  code: 'order_request_no_transport_orders'

  meta: never

  @ApiErrorStatus(HttpStatus.BAD_REQUEST)
  declare status: '400'

  constructor (orderRequestUuid: string) {
    super(`Order request ${orderRequestUuid} has no requested transport orders and cannot be accepted`)
    this.code = 'order_request_no_transport_orders'
    this.status = '400'
  }
}

import { HttpStatus } from '@nestjs/common'
import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { ApiError } from '../../../../modules/exceptions/api-errors/api-error.js'
import { ApiErrorStatus } from '../../../../modules/exceptions/api-errors/api-error-status.decorator.js'

export class OrderRequestValidationError extends ApiError {
  @ApiErrorCode('order_request_validation_failed')
  code: 'order_request_validation_failed'

  meta: {
    validationErrors: Array<{
      requestedTransportOrderUuid: string
      missingFields: string[]
      nonAllowedFields: string[]
    }>
  }

  @ApiErrorStatus(HttpStatus.BAD_REQUEST)
  declare status: '400'

  constructor (validationErrors: Array<{
    requestedTransportOrderUuid: string
    missingFields: string[]
    nonAllowedFields: string[]
  }>) {
    const errorCount = validationErrors.length
    super(`Order request validation failed for ${errorCount} requested transport order(s)`)
    this.code = 'order_request_validation_failed'
    this.status = '400'
    this.meta = { validationErrors }
  }
}

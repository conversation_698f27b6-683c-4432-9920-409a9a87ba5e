import { Api<PERSON><PERSON><PERSON>, ApiOAuth2 } from '@nestjs/swagger'
import { Controller, HttpCode, HttpStatus, Post } from '@nestjs/common'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { OrderRequestNotFoundError } from '../../errors/order-request-not-found.error.js'
import { OrderRequestInvalidStatusError } from '../../errors/order-request-invalid-status.error.js'
import { OrderRequestValidationError } from '../../errors/order-request-validation.error.js'
import { OrderRequestNoTransportOrdersError } from '../../errors/order-request-no-transport-orders.error.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { AcceptOrderRequestUseCase } from './accept-order-request.use-case.js'

@ApiTags('Order Request')
@ApiOAuth2([])
@Controller('/order-requests/:orderRequestUuid/accept')
export class AcceptOrderRequestController {
  constructor (
    private readonly useCase: AcceptOrderRequestUseCase
  ) {}

  @Post()
  @HttpCode(HttpStatus.NO_CONTENT)
  @Permissions(Permission.ORDER_REQUEST_UPDATE)
  @ApiNotFoundErrorResponse(OrderRequestNotFoundError)
  @ApiBadRequestErrorResponse(
    OrderRequestInvalidStatusError,
    OrderRequestValidationError,
    OrderRequestNoTransportOrdersError
  )
  async acceptOrderRequest (
    @UuidParam('orderRequestUuid') orderRequestUuid: string
  ): Promise<void> {
    await this.useCase.execute(orderRequestUuid)
  }
}

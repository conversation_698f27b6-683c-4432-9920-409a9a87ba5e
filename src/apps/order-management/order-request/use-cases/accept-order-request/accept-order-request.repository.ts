import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { RequestedTransportOrder } from '../../../requested-transport-order/requested-transport-order.entity.js'
import { OrderRequestStatus } from '../../enums/order-request-status.js'

@Injectable()
export class AcceptOrderRequestRepository {
  constructor (
    @InjectRepository(OrderRequest)
    private readonly orderRequestRepository: Repository<OrderRequest>,
    @InjectRepository(RequestedTransportOrder)
    private readonly requestedTransportOrderRepository: Repository<RequestedTransportOrder>
  ) {}

  async findOrderRequestWithDetails (orderRequestUuid: string): Promise<OrderRequest | null> {
    return await this.orderRequestRepository.findOne({
      where: { uuid: orderRequestUuid },
      relations: {
        contract: {
          contractType: true
        },
        requestedTransportOrders: true
      }
    })
  }

  async findRequestedTransportOrders (uuid: string): Promise<RequestedTransportOrder[]> {
    return await this.requestedTransportOrderRepository.find({
      where: { orderRequestUuid: uuid },
      relations: {
        orderRequest: {
          contract: {
            contractType: true
          }
        }
      }
    })
  }

  async acceptOrderRequest (
    orderRequestUuid: string,
    acceptedByUserUuid: string
  ): Promise<void> {
    await this.orderRequestRepository.update(orderRequestUuid, {
      status: OrderRequestStatus.ACCEPTED,
      acceptedByUserUuid,
      updatedByUserUuid: acceptedByUserUuid
    })
  }
}

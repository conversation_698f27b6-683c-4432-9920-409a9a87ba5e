import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'

@OneOfMeta(DomainEventLog, DomainEventType.ORDER_REQUEST_ACCEPTED)
export class OrderRequestAcceptedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly orderRequestUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly acceptedByUserUuid: string

  constructor (orderRequestUuid: string, acceptedByUserUuid: string) {
    this.orderRequestUuid = orderRequestUuid
    this.acceptedByUserUuid = acceptedByUserUuid
  }
}

@RegisterDomainEvent(DomainEventType.ORDER_REQUEST_ACCEPTED, 1)
export class OrderRequestAcceptedEvent extends DomainEvent<OrderRequestAcceptedEventContent> {
  constructor (orderRequestUuid: string, acceptedByUserUuid: string) {
    super({ content: new OrderRequestAcceptedEventContent(orderRequestUuid, acceptedByUserUuid) })
  }
}

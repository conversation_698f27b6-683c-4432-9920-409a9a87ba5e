import { ApiProperty } from '@nestjs/swagger'
import { IsBoolean, IsEnum, IsNotEmpty, IsString, IsUUI<PERSON>, <PERSON>, <PERSON> } from 'class-validator'
import { IsDateWithoutTimeString, IsNullable } from '@wisemen/validators'
import { CreateOrderRequestClientTypeApiProperty, CreateOrderRequestClientType } from './types/create-order-request-client-type.js'
import { CreateOrderRequestPassengerTypeApiProperty, CreateOrderRequestPassengerType } from './types/create-order-request-passenger-type.js'

export class CreateOrderRequestCommand {
  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  @IsNotEmpty()
  clientUuid: string

  @CreateOrderRequestClientTypeApiProperty()
  @IsEnum(CreateOrderRequestClientType)
  @IsNotEmpty()
  clientType: CreateOrderRequestClientType

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  @IsNotEmpty()
  contractUuid: string

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  @IsNotEmpty()
  passengerUuid: string

  @CreateOrderRequestPassengerTypeApiProperty()
  @IsEnum(CreateOrderRequestPassengerType)
  @IsNotEmpty()
  passengerType: CreateOrderRequestPassengerType

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  isRecurring: boolean

  @ApiProperty({ type: String, format: 'date', nullable: true })
  @IsNullable()
  @IsDateWithoutTimeString()
  startDate: string | null

  @ApiProperty({ type: Number, nullable: true })
  @IsNullable()
  @Min(1)
  @Max(4)
  iterationIntervalWeeks: number | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  remarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  remarksForPlanner: string | null
}

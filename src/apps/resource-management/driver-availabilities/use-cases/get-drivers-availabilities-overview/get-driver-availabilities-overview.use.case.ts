import { Injectable } from '@nestjs/common'
import { Any, LessThanOrEqual, MoreThan<PERSON>r<PERSON>qual, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import dayjs, { Dayjs } from 'dayjs'
import { SortDirection } from '@wisemen/pagination'
import { SearchParams } from 'typesense/lib/Typesense/Documents.js'
import { TypesenseQueryService } from '../../../../../modules/typesense/services/typesense-query.service.js'
import { TypesenseCollectionName } from '../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { TypesenseOperationMode } from '../../../../../modules/typesense/param-builders/enums/typesense-operation-mode.enum.js'
import { TypesenseSearchParamsBuilder } from '../../../../../modules/typesense/param-builders/search-params.builder.js'
import { DriverTypesenseCollection } from '../../../driver/typesense/driver.collection.js'
import { DriverUnavailability } from '../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { Driver } from '../../../driver/entities/driver/driver.entity.js'
import { DriverAvailabilityCombiner } from '../../utils/availability-combiner/driver-availability-combiner.js'
import { DriverAvailabilityRepository } from '../../repositories/driver-availability.repository.js'
import { DriverUuid } from '../../../driver/entities/driver.uuid.js'
import { SortField } from '../../../../../modules/typesense/collections/typesense.collection.js'
import { orderItems } from '../../../../../utils/ordering/order-items.js'
import { DriverAvailability } from '../../../driver/entities/driver-availability/driver-availability.entity.js'
import { VehicleUnavailability } from '../../../vehicle-unavailability/entities/vehicle-unavailability.entity.js'
import { GetDriverAvailabilitiesOverviewResponse } from './get-driver-availabilities-overview.response.js'
import { DriverPeriodAvailability } from './driver-period-availability.js'
import { DriverDayAvailability } from './driver-day-availability.js'
import { GetDriverAvailabilitiesOverviewQuery } from './query/get-driver-availabilities-overview.query.js'
import { GetDriverAvailabilitiesOverviewSortQueryKey } from './query/get-driver-availabilities-overview.sort-query.js'

@Injectable()
export class GetDriverAvailabilitiesOverviewUseCase {
  constructor (
    private readonly typesense: TypesenseQueryService,
    private readonly driverAvailabilityRepository: DriverAvailabilityRepository,
    @InjectRepository(DriverUnavailability)
    private readonly driverUnavailabilityRepository: Repository<DriverUnavailability>,
    @InjectRepository(Driver)
    private readonly driverRepository: Repository<Driver>
  ) {}

  async getAvailabilitiesOverview (
    query: GetDriverAvailabilitiesOverviewQuery
  ): Promise<GetDriverAvailabilitiesOverviewResponse> {
    const searchParams = this.buildTypesenseSearchParams(query)
    const drivers = await this.typesense.search(TypesenseCollectionName.DRIVER, searchParams)

    const from = dayjs(query.filter.from)
    const until = dayjs(query.filter.until)

    const driverUuids = drivers.items.map(driver => driver.id)
    const driversWithDefaultVehicles = await this.getDriversWithDefaultVehicles(driverUuids)

    if (driverUuids.length === 0) {
      return new GetDriverAvailabilitiesOverviewResponse([], drivers.meta)
    }

    const orderedDriversWithDefaultVehicles
      = orderItems(driversWithDefaultVehicles, driverUuids, 'uuid')

    const [
      driverAvailabilities,
      driverUnavailabilities,
      driverVehicleUnavailabilities
    ] = await Promise.all([
      this.driverAvailabilityRepository.findDriverAvailabilities(from, until, driverUuids),
      this.findDriverUnavailabilities(from, until, driverUuids),
      this.findDriverVehicleUnavailabilities(from, until, driverUuids)
    ])

    const driverPeriods: DriverPeriodAvailability[] = []
    for (const driver of orderedDriversWithDefaultVehicles) {
      const mappedDriverAvailability = driverAvailabilities
        .filter(availability => availability.driverUuid === driver.uuid)
      const mappedDriverUnavailabilities = driverUnavailabilities
        .filter(unavailability => unavailability.driverUuid === driver.uuid)
      const mappedVehicleUnavailabilities = driverVehicleUnavailabilities
        .filter(driverVehicleUnavailabilities => driverVehicleUnavailabilities.uuid === driver.uuid)
        .map(vehicle => vehicle.defaultVehicle?.unavailabilities || []).flat()

      const dayAvailabilities = this.getDriverDayAvailabilities(
        from,
        until,
        mappedDriverAvailability,
        mappedDriverUnavailabilities,
        mappedVehicleUnavailabilities,
        driver.deactivatedAt ? dayjs(driver.deactivatedAt) : null
      )

      driverPeriods.push(new DriverPeriodAvailability(driver, dayAvailabilities))
    }

    return new GetDriverAvailabilitiesOverviewResponse(driverPeriods, drivers.meta)
  }

  private getDriverDayAvailabilities (
    from: Dayjs,
    until: Dayjs,
    driverAvailabilities: DriverAvailability[],
    driverUnavailabilities: DriverUnavailability[],
    driverVehicleUnavailabilities: VehicleUnavailability[],
    deactivatedAt: Dayjs | null
  ): DriverDayAvailability[] {
    if (deactivatedAt && deactivatedAt.isBefore(from)) {
      return []
    }

    const effectiveUntil = (deactivatedAt && deactivatedAt.isBefore(until))
      ? deactivatedAt
      : until

    const periodAvailabilities = new DriverAvailabilityCombiner(
      from,
      effectiveUntil,
      driverAvailabilities,
      driverUnavailabilities,
      driverVehicleUnavailabilities
    ).getByDay()

    return Array.from(periodAvailabilities.entries()).map(([day, slots]) => {
      return new DriverDayAvailability(day, slots)
    })
  }

  private async findDriverUnavailabilities (
    from: Dayjs,
    until: Dayjs,
    driverUuids: string[]
  ): Promise<DriverUnavailability[]> {
    return await this.driverUnavailabilityRepository.find({ where: {
      driverUuid: Any(driverUuids),
      from: LessThanOrEqual(until.toDate()),
      until: MoreThanOrEqual(from.toDate())
    }
    })
  }

  private async findDriverVehicleUnavailabilities (
    from: Dayjs,
    until: Dayjs,
    driverUuids: string[]
  ): Promise<Driver[]> {
    const driversWithVehicleUnavailabilities = await this.driverRepository.createQueryBuilder('driver')
      .leftJoinAndSelect('driver.defaultVehicle', 'vehicle')
      .leftJoinAndSelect('vehicle.unavailabilities', 'vehicleUnavailability')
      .where('driver.uuid IN (:...uuids)', { uuids: driverUuids })
      .andWhere('vehicleUnavailability.from <= :until', { until: until.toDate() })
      .andWhere('vehicleUnavailability.until >= :from', { from: from.toDate() })
      .getMany()

    return driversWithVehicleUnavailabilities
  }

  private async getDriversWithDefaultVehicles (
    driverUuids: DriverUuid[]
  ): Promise<Driver[]> {
    return this.driverRepository.find({
      where: { uuid: Any(driverUuids) },
      relations: {
        defaultVehicle: true
      }
    })
  }

  private mapSortKeyToField (
    key: GetDriverAvailabilitiesOverviewSortQueryKey
  ): SortField<DriverTypesenseCollection> {
    switch (key) {
      case GetDriverAvailabilitiesOverviewSortQueryKey.FULL_NAME: return 'fullName'
      default: return key
    }
  }

  private buildTypesenseSearchParams (
    query: GetDriverAvailabilitiesOverviewQuery
  ): SearchParams {
    const searchParamsBuilder = new TypesenseSearchParamsBuilder<DriverTypesenseCollection>()
      .withQuery(query.search)
      .addSearchOn('fullName', TypesenseOperationMode.ALWAYS)
      .withOffset(query.pagination?.offset)
      .withLimit(query.pagination?.limit)

    if (query.sort !== undefined) {
      for (const sort of query.sort) {
        const field = this.mapSortKeyToField(sort.key)
        searchParamsBuilder.addSortOn(field, sort.order)
      }
    } else {
      searchParamsBuilder.addSortOn('fullName', SortDirection.ASC)
    }

    return searchParamsBuilder.build()
  }
}

import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { stringify } from 'qs'
import { DateRange } from '@wisemen/date-range'
import { WiseDate, FutureInfinityDate } from '@wisemen/wise-date'
import { SortDirection } from '@wisemen/pagination'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { GetDriverAvailabilitiesOverviewQueryBuilder } from '../get-driver-availabilities-overview.query.builder.js'
import { DriverEntityBuilder } from '../../../../driver/entities/driver/driver.entity.builder.js'
import { Driver } from '../../../../driver/entities/driver/driver.entity.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { TypesenseCollectionName } from '../../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollectionService } from '../../../../../../modules/typesense/services/typesense-collection.service.js'
import { MigrateCollectionsUseCase } from '../../../../../../modules/typesense/use-cases/migrate-collections/migrate-collections.use-case.js'
import { IsoWeekday } from '../../../../../../utils/dates/iso-weekday.js'
import { Timezone } from '../../../../../../utils/dates/timezone.enum.js'
import { DriverAvailabilityEntityBuilder } from '../../../../driver/entities/driver-availability/driver-availability.entity.builder.js'
import { DriverUnavailabilityEntityBuilder } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity-builder.js'
import { DriverUnavailabilityReason } from '../../../../driver/enums/driver-unavailability.enum.js'
import { DriverAvailability } from '../../../../driver/entities/driver-availability/driver-availability.entity.js'
import { DriverUnavailability } from '../../../../driver/entities/driver-unavailability/driver-unavailability.entity.js'
import { DriverUuid } from '../../../../driver/entities/driver/driver.uuid.js'
import { GetDriverAvailabilitiesOverviewResponse, GetDriverAvailabilitiesOverviewResponseItem } from '../get-driver-availabilities-overview.response.js'
import { VehicleEntityBuilder } from '../../../../vehicle/entities/vehicle.entity-builder.js'
import { Vehicle } from '../../../../vehicle/entities/vehicle.entity.js'
import { DriverAvailabilityTimeSlotType } from '../driver-day-availability-slot.type.js'
import { GetDriverAvailabilitiesOverviewSortQueryKey } from '../query/get-driver-availabilities-overview.sort-query.js'
import { VehicleUnavailability } from '../../../../vehicle-unavailability/entities/vehicle-unavailability.entity.js'
import { VehicleUnavailabilityEntityBuilder } from '../../../../vehicle-unavailability/entities/vechile-unavailability.enitity-builder.js'

describe('Get driver availabililties overview e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  let driverWithAvailabilityUuid: DriverUuid
  let driverWithoutVehicleUuid: DriverUuid
  let vehicle: Vehicle

  let driverAlice: DriverUuid
  let driverBob: DriverUuid
  let driverCharlie: DriverUuid
  let driverAvailability: DriverAvailability
  let driverUnavailability: DriverUnavailability
  let vehicleUnavailability: VehicleUnavailability

  let typesenseCollectionService: TypesenseCollectionService

  let branch: Branch

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    branch = new BranchEntityBuilder().build()
    const location = new LocationBuilder().build()

    vehicle = new VehicleEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withPermitBranchUuid(branch.uuid)
      .withDepotLocationUuid(location.uuid)
      .withStartLocationUuid(location.uuid)
      .withEndLocationUuid(location.uuid)
      .build()

    const [driver1, driver2, driver3] = [
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withFirstName('Alice')
        .withDefaultVehicleUuid(vehicle.uuid)
        .build(),
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withFirstName('Charlie')
        .build(),
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withFirstName('Bob')
        .build(),
      new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withFirstName('Deactivated')
        .withLastName('Driver')
        .withDeactivatedAt(new Date('2025-06-25'))
        .build()
    ]

    driverAlice = driver1.uuid
    driverCharlie = driver2.uuid
    driverBob = driver3.uuid

    await setup.dataSource.manager.insert(Branch, branch)
    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(Vehicle, vehicle)
    await setup.dataSource.manager.insert(Driver, [driver1, driver2, driver3])
    driverWithAvailabilityUuid = driver1.uuid
    driverWithoutVehicleUuid = driver2.uuid

    const typesenseMigrator = setup.testModule.get(MigrateCollectionsUseCase, { strict: false })
    typesenseCollectionService = setup.testModule.get(TypesenseCollectionService)

    await typesenseMigrator.execute(true, [TypesenseCollectionName.DRIVER])
    await typesenseCollectionService
      .importManually(TypesenseCollectionName.DRIVER, [driver1, driver2, driver3])

    driverAvailability = new DriverAvailabilityEntityBuilder()
      .withDriverUuid(driver1.uuid)
      .withDateRange(new DateRange(
        new WiseDate('2025-06-01'),
        new FutureInfinityDate()
      ))
      .withPeriod(1)
      .withTimezone(Timezone.EUROPE_BRUSSELS)
      .withSlots([{
        startTime: '08:00',
        endTime: '12:00',
        offset: 0,
        weekday: IsoWeekday.MONDAY
      }])
      .build()

    driverUnavailability = new DriverUnavailabilityEntityBuilder()
      .withDriverUuid(driver1.uuid)
      .withFrom(new Date('2025-06-02T08:00:00.000Z'))
      .withUntil(new Date('2025-06-02T10:00:00.000Z'))
      .withReason(DriverUnavailabilityReason.VACATION)
      .build()

    vehicleUnavailability = new VehicleUnavailabilityEntityBuilder()
      .withVehicleUuid(vehicle.uuid)
      .withFrom(new Date('2025-06-25T08:00:00.000Z'))
      .withUntil(new Date('2025-07-01T10:00:00.000Z'))
      .withRemark('Vehicle is under maintenance')
      .build()

    await setup.dataSource.manager.insert(DriverAvailability, driverAvailability)
    await setup.dataSource.manager.insert(DriverUnavailability, driverUnavailability)
    await setup.dataSource.manager.insert(VehicleUnavailability, vehicleUnavailability)
  })

  after(async () => await setup.teardown())

  describe('pagination', () => {
    it('returns driver availabilities in a paginated format, taking into account deactivatedAt date', async () => {
      const query = new GetDriverAvailabilitiesOverviewQueryBuilder()
        .withFrom('2025-06-01')
        .withUntil('2025-06-30')
        .build()

      const response = await request(setup.httpServer)
        .get('/drivers-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)

      const body = response.body as GetDriverAvailabilitiesOverviewResponse

      const driver1Response = body.items.find(
        (item: GetDriverAvailabilitiesOverviewResponseItem) =>
          item.driver.uuid === driverWithAvailabilityUuid
      )

      const driver2Response = body.items.find(
        (item: GetDriverAvailabilitiesOverviewResponseItem) =>
          item.driver.uuid === driverWithoutVehicleUuid
      )

      expect(driver2Response).toBeDefined()
      expect(driver2Response?.driver.vehicle).toBeNull()

      expect(driver1Response).toBeDefined()
      expect(driver1Response).toEqual(
        {
          driver: {
            uuid: driverWithAvailabilityUuid,
            fullName: 'Alice Doe',
            deactivatedAt: null,
            vehicle: {
              uuid: vehicle.uuid,
              licensePlate: vehicle.licensePlate
            }
          },
          availabilities: [
            {
              date: '2025-06-02',
              slots: [
                {
                  uuid: driverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-02T06:00:00.000Z',
                  end: '2025-06-02T08:00:00.000Z',
                  reason: null
                },
                {
                  uuid: driverUnavailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_UNAVAILABLE,
                  start: '2025-06-02T08:00:00.000Z',
                  end: '2025-06-02T10:00:00.000Z',
                  reason: DriverUnavailabilityReason.VACATION
                }
              ]
            },
            {
              date: '2025-06-09',
              slots: [
                {
                  uuid: driverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-09T06:00:00.000Z',
                  end: '2025-06-09T10:00:00.000Z',
                  reason: null
                }
              ]
            },
            {
              date: '2025-06-16',
              slots: [
                {
                  uuid: driverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-16T06:00:00.000Z',
                  end: '2025-06-16T10:00:00.000Z',
                  reason: null
                }
              ]
            },
            {
              date: '2025-06-23',
              slots: [
                {
                  uuid: driverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-23T06:00:00.000Z',
                  end: '2025-06-23T10:00:00.000Z',
                  reason: null
                }
              ]
            },
            {
              date: '2025-06-30',
              slots: [
                {
                  uuid: vehicleUnavailability.uuid,
                  type: DriverAvailabilityTimeSlotType.VEHICLE_UNAVAILABLE,
                  start: '2025-06-30T06:00:00.000Z',
                  end: '2025-06-30T10:00:00.000Z',
                  reason: null
                }
              ]
            }
          ]
        }
      )
    })
  })

  describe('query', () => {
    it('allows sorts drivers on full name ASC', async () => {
      const query = new GetDriverAvailabilitiesOverviewQueryBuilder()
        .withFrom('2025-06-01')
        .withUntil('2025-06-30')
        .withSort(GetDriverAvailabilitiesOverviewSortQueryKey.FULL_NAME, SortDirection.ASC)
        .build()

      const response = await request(setup.httpServer)
        .get('/drivers-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)
      expect(response.body.items[0].driver.uuid).toEqual(driverAlice)
      expect(response.body.items[1].driver.uuid).toEqual(driverBob)
      expect(response.body.items[2].driver.uuid).toEqual(driverCharlie)
    })

    it('allows sorts drivers on full name DESC', async () => {
      const query = new GetDriverAvailabilitiesOverviewQueryBuilder()
        .withFrom('2025-06-01')
        .withUntil('2025-06-30')
        .withSort(GetDriverAvailabilitiesOverviewSortQueryKey.FULL_NAME, SortDirection.DESC)
        .build()

      const response = await request(setup.httpServer)
        .get('/drivers-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)
      expect(response.body.items[0].driver.uuid).toEqual(driverCharlie)
      expect(response.body.items[1].driver.uuid).toEqual(driverBob)
      expect(response.body.items[2].driver.uuid).toEqual(driverAlice)
    })
  })

  describe(`deactivatedAt date`, () => {
    let deactivatedDriver: Driver
    let earlyDeactivatedDriver: Driver
    let deactivatedDriverAvailability: DriverAvailability
    let earlyDeactivatedDriverAvailability: DriverAvailability
    before(async () => {
      deactivatedDriver = new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withFirstName('Deactivated')
        .withLastName('Driver')
        .withDeactivatedAt(new Date('2025-06-22'))
        .build()

      earlyDeactivatedDriver = new DriverEntityBuilder()
        .withBranchUuid(branch.uuid)
        .withFirstName('Deactivated')
        .withLastName('Driver')
        .withDeactivatedAt(new Date('2020-06-22'))
        .build()

      await setup.dataSource.manager.insert(Driver, [deactivatedDriver, earlyDeactivatedDriver])

      await typesenseCollectionService
        .importManually(TypesenseCollectionName.DRIVER, [deactivatedDriver, earlyDeactivatedDriver])

      deactivatedDriverAvailability = new DriverAvailabilityEntityBuilder()
        .withDriverUuid(deactivatedDriver.uuid)
        .withDateRange(new DateRange(
          new WiseDate('2025-06-01'),
          new FutureInfinityDate()
        ))
        .withPeriod(1)
        .withTimezone(Timezone.EUROPE_BRUSSELS)
        .withSlots([{
          startTime: '08:00',
          endTime: '12:00',
          offset: 0,
          weekday: IsoWeekday.MONDAY
        }])
        .build()

      earlyDeactivatedDriverAvailability = new DriverAvailabilityEntityBuilder()
        .withDriverUuid(deactivatedDriver.uuid)
        .withDateRange(new DateRange(
          new WiseDate('2025-06-01'),
          new FutureInfinityDate()
        ))
        .withPeriod(1)
        .withTimezone(Timezone.EUROPE_BRUSSELS)
        .withSlots([{
          startTime: '08:00',
          endTime: '12:00',
          offset: 0,
          weekday: IsoWeekday.MONDAY
        }])
        .build()

      await setup.dataSource.manager.insert(DriverAvailability, [
        deactivatedDriverAvailability,
        earlyDeactivatedDriverAvailability
      ])
    })
    it('takes into account the deactivationDate in the middle of period', async () => {
      const query = new GetDriverAvailabilitiesOverviewQueryBuilder()
        .withFrom('2025-06-01')
        .withUntil('2025-06-30')
        .build()

      const response = await request(setup.httpServer)
        .get('/drivers-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)

      const body = response.body as GetDriverAvailabilitiesOverviewResponse
      const driver = body.items.find(
        (item: GetDriverAvailabilitiesOverviewResponseItem) =>
          item.driver.uuid === deactivatedDriver.uuid
      )

      expect(driver).toBeDefined()
      expect(driver).toEqual(
        {
          driver: {
            uuid: deactivatedDriver.uuid,
            fullName: 'Deactivated Driver',
            deactivatedAt: new Date('2025-06-22').toISOString(),
            vehicle: null
          },
          availabilities: [
            {
              date: '2025-06-02',
              slots: [
                {
                  uuid: deactivatedDriverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-02T06:00:00.000Z',
                  end: '2025-06-02T10:00:00.000Z',
                  reason: null
                }
              ]
            },
            {
              date: '2025-06-09',
              slots: [
                {
                  uuid: deactivatedDriverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-09T06:00:00.000Z',
                  end: '2025-06-09T10:00:00.000Z',
                  reason: null
                }
              ]
            },
            {
              date: '2025-06-16',
              slots: [
                {
                  uuid: deactivatedDriverAvailability.uuid,
                  type: DriverAvailabilityTimeSlotType.DRIVER_AVAILABLE,
                  start: '2025-06-16T06:00:00.000Z',
                  end: '2025-06-16T10:00:00.000Z',
                  reason: null
                }
              ]
            }
          ]
        }
      )
    })

    it('takes into account the deactivationDate in before the query period', async () => {
      const query = new GetDriverAvailabilitiesOverviewQueryBuilder()
        .withFrom('2025-06-01')
        .withUntil('2025-06-30')
        .build()

      const response = await request(setup.httpServer)
        .get('/drivers-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)

      const body = response.body as GetDriverAvailabilitiesOverviewResponse
      const driver = body.items.find(
        (item: GetDriverAvailabilitiesOverviewResponseItem) =>
          item.driver.uuid === earlyDeactivatedDriver.uuid
      )

      expect(driver).toBeDefined()
      expect(driver).toEqual(
        {
          driver: {
            uuid: earlyDeactivatedDriver.uuid,
            fullName: 'Deactivated Driver',
            deactivatedAt: new Date('2020-06-22').toISOString(),
            vehicle: null
          },
          availabilities: []
        }
      )
    })
  })
})

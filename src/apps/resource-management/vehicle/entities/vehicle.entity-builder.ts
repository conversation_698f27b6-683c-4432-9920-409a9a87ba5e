import { randomUUID } from 'crypto'
import { VehiclePermitType } from '../types/vehicle-permit.type.js'
import { Vehicle } from './vehicle.entity.js'
import { generateVehicleUuid } from './vehicle.uuid.js'

export class VehicleEntityBuilder {
  private vehicle: Vehicle

  constructor () {
    this.vehicle = new Vehicle()
    this.vehicle.uuid = generateVehicleUuid()
    this.vehicle.createdAt = new Date()
    this.vehicle.updatedAt = new Date()
    this.vehicle.chassisNumber = 'ABC123456789'
    this.vehicle.licensePlate = '1-ABC-123'
    this.vehicle.capacity = 4
    this.vehicle.bocoId = 'BOCO123'
    this.vehicle.branchUuid = 'branch-uuid'
    this.vehicle.permitType = VehiclePermitType.VEHICLE_PERMIT_BRUSSELS
    this.vehicle.permitBranchUuid = randomUUID()
    this.vehicle.permitChiron = null
    this.vehicle.depotLocationUuid = randomUUID()
    this.vehicle.startLocationUuid = randomUUID()
    this.vehicle.endLocationUuid = randomUUID()
  }

  withHendriksId (hendriksId: string | null): this {
    this.vehicle.hendriksId = hendriksId
    return this
  }

  withDeactivatedAt (deactivatedAt: Date | null): this {
    this.vehicle.deactivatedAt = deactivatedAt
    return this
  }

  withChassisNumber (chassisNumber: string): this {
    this.vehicle.chassisNumber = chassisNumber
    return this
  }

  withLicensePlate (licensePlate: string): this {
    this.vehicle.licensePlate = licensePlate
    return this
  }

  withCapacity (capacity: number): this {
    this.vehicle.capacity = capacity
    return this
  }

  withBocoId (bocoId: string): this {
    this.vehicle.bocoId = bocoId
    return this
  }

  withBranchUuid (branchUuid: string): this {
    this.vehicle.branchUuid = branchUuid
    return this
  }

  withPermitType (permitType: VehiclePermitType): this {
    this.vehicle.permitType = permitType
    return this
  }

  withPermitBranchUuid (permitBranchUuid: string): this {
    this.vehicle.permitBranchUuid = permitBranchUuid
    return this
  }

  withPermitChiron (permitChiron?: string | null): this {
    this.vehicle.permitChiron = permitChiron ?? null
    return this
  }

  withDepotLocationUuid (depotLocationUuid: string): this {
    this.vehicle.depotLocationUuid = depotLocationUuid
    return this
  }

  withStartLocationUuid (startLocationUuid: string): this {
    this.vehicle.startLocationUuid = startLocationUuid
    return this
  }

  withEndLocationUuid (endLocationUuid: string): this {
    this.vehicle.endLocationUuid = endLocationUuid
    return this
  }

  build (): Vehicle {
    return this.vehicle
  }
}

import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../../../modules/exceptions/api-errors/not-found.api-error.js'
import { VehicleUuid } from '../entities/vehicle.uuid.js'

export class VehicleNotFoundError extends NotFoundApiError {
  @ApiErrorCode('vehicle_not_found')
  code = 'vehicle_not_found'

  meta: never

  constructor (uuid?: VehicleUuid) {
    super(`Vehicle ${uuid} not found`)
  }
}

import { Any, DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Injectable } from '@nestjs/common'
import { Vehicle } from '../../entities/vehicle.entity.js'
import { VehicleEntityBuilder } from '../../entities/vehicle.entity-builder.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { LocationUuid } from '../../../../../modules/location/entities/location.uuid.js'
import { Location } from '../../../../../modules/location/entities/location.entity.js'
import { LocationNotFoundApiError } from '../../../../../modules/location/location-not-found.api-error.js'
import { CreateVehicleCommand } from './create-vehicle.command.js'
import { CreateVehicleResponse } from './create-vehicle.response.js'
import { VehicleCreatedEvent } from './vehicle-created.event.js'

@Injectable()
export class CreateVehicleUseCase {
  constructor (
    @InjectRepository(Vehicle)
    private vehicleRepository: Repository<Vehicle>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
    private dataSource: DataSource,
    private eventEmitter: DomainEventEmitter
  ) {}

  public async execute (
    command: CreateVehicleCommand
  ): Promise<CreateVehicleResponse> {
    const vehicle = new VehicleEntityBuilder()
      .withChassisNumber(command.chassisNumber)
      .withLicensePlate(command.licensePlate)
      .withCapacity(command.capacity)
      .withBocoId(command.bocoId)
      .withPermitChiron(command.permitChiron)
      .withPermitType(command.permitType)
      .withDepotLocationUuid(command.depotLocationUuid)
      .withStartLocationUuid(command.startLocationUuid)
      .withEndLocationUuid(command.endLocationUuid)
      .withPermitBranchUuid(command.permitBranchUuid)
      .withBranchUuid(command.branchUuid)
      .build()

    await this.assertLocationsExist(command)

    await transaction (this.dataSource, async () => {
      await this.vehicleRepository.insert(vehicle)
      await this.eventEmitter.emitOne(new VehicleCreatedEvent(vehicle.uuid))
    })

    return new CreateVehicleResponse(vehicle)
  }

  private async assertLocationsExist (command: CreateVehicleCommand): Promise<void> {
    const locationUuids = [command.startLocationUuid, command.endLocationUuid] as LocationUuid[]

    const locations = await this.locationRepository.findBy({ uuid: Any(locationUuids) })

    for (const locationUuid of locationUuids) {
      const locationExists = locations.some(location => location.uuid === locationUuid)
      if (!locationExists) {
        throw new LocationNotFoundApiError(locationUuid)
      }
    }
  }
}

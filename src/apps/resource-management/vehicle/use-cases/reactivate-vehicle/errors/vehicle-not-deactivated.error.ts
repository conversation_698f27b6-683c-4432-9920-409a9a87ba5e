import { ApiErrorCode } from '../../../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { ConflictApiError } from '../../../../../../modules/exceptions/api-errors/conflict.api-error.js'

export class VehicleNotDeactivatedError extends ConflictApiError {
  @ApiErrorCode('vehicle_not_deactivated')
  code = 'vehicle_not_deactivated'

  meta: never

  constructor () {
    super(`Vehicle is not deactivated`)
  }
}

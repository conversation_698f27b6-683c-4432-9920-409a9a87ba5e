import { Controller, HttpCode, Post } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { VehicleNotFoundError } from '../../errors/vehicle-not-found.error.js'
import { VehicleUuid } from '../../entities/vehicle.uuid.js'
import { ReactivateVehicleUseCase } from './reactivate-vehicle.use-case.js'
import { VehicleNotDeactivatedError } from './errors/vehicle-not-deactivated.error.js'

@ApiTags('Vehicles')
@ApiOAuth2([])
@Controller('vehicles/:vehicleUuid/reactivate')
export class ReactivateVehicleController {
  constructor (
    private readonly useCase: ReactivateVehicleUseCase
  ) { }

  @Post()
  @HttpCode(200)
  @Permissions(Permission.VEHICLE_REACTIVATE)
  @ApiNotFoundErrorResponse(VehicleNotFoundError)
  @ApiConflictErrorResponse(VehicleNotDeactivatedError)
  public async reactivateVehicle (
    @UuidParam('vehicleUuid') vehicleUuid: VehicleUuid
  ): Promise<void> {
    await this.useCase.execute(vehicleUuid)
  }
}

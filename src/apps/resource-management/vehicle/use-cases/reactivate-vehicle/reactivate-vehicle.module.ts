import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { Vehicle } from '../../entities/vehicle.entity.js'
import { ReactivateVehicleController } from './reactivate-vehicle.controller.js'
import { ReactivateVehicleUseCase } from './reactivate-vehicle.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Vehicle]),
    DomainEventEmitterModule
  ],
  controllers: [
    ReactivateVehicleController
  ],
  providers: [
    ReactivateVehicleUseCase
  ]
})
export class ReactivateVehicleModule {}

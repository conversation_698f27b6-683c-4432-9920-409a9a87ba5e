import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, IsNull, Not, Repository } from 'typeorm'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { VehicleNotFoundError } from '../../errors/vehicle-not-found.error.js'
import { Vehicle } from '../../entities/vehicle.entity.js'
import { VehicleUuid } from '../../entities/vehicle.uuid.js'
import { VehicleNotDeactivatedError } from './errors/vehicle-not-deactivated.error.js'
import { VehicleReactivatedEvent } from './vehicle-reactivated.event.js'

@Injectable()
export class ReactivateVehicleUseCase {
  constructor (
    @InjectRepository(Vehicle)
    private vehicleRepository: Repository<Vehicle>,
    private domainEventEmitter: DomainEventEmitter,
    private dataSource: DataSource
  ) {}

  public async execute (
    uuid: VehicleUuid
  ): Promise<void> {
    await this.validateVehicleExists(uuid)
    await this.validateVehicleIsDeactivated(uuid)

    await transaction(this.dataSource, async () => {
      await this.vehicleRepository.update(uuid, {
        deactivatedAt: null
      })
      await this.domainEventEmitter.emitOne(new VehicleReactivatedEvent(uuid))
    })
  }

  private async validateVehicleExists (vehicleUuid: VehicleUuid): Promise<void> {
    const vehicleExists = await this.vehicleRepository.findOneBy({
      uuid: vehicleUuid
    })

    if (!vehicleExists) {
      throw new VehicleNotFoundError()
    }
  }

  private async validateVehicleIsDeactivated (vehicleUuid: VehicleUuid): Promise<void> {
    const deactivatedVehicleExists = await this.vehicleRepository.existsBy({
      uuid: vehicleUuid,
      deactivatedAt: Not(IsNull())
    })

    if (!deactivatedVehicleExists) {
      throw new VehicleNotDeactivatedError()
    }
  }
}

import { before, describe, it, after } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import type { DataSource } from 'typeorm'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestAuthContext } from '../../../../../../../test/utils/test-auth-context.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { BranchEntityBuilder } from '../../../../branch/branch.entity-builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { LocationEntityBuilder } from '../../../../../../modules/location/entities/location.entity-builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'
import { VehicleEntityBuilder } from '../../../entities/vehicle.entity-builder.js'
import { Vehicle } from '../../../entities/vehicle.entity.js'

describe('Reactivate vehicle end to end tests', () => {
  let setup: EndToEndTestSetup
  let dataSource: DataSource
  let context: TestAuthContext
  let adminUser: TestUser
  let defaultUser: TestUser

  let vehicle: Vehicle

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    context = setup.authContext
    dataSource = setup.dataSource

    adminUser = await context.getAdminUser()
    defaultUser = await context.getDefaultUser()

    const location = new LocationEntityBuilder()
      .withAddress(new AddressBuilder().build())
      .build()
    const branch = new BranchEntityBuilder().build()

    vehicle = new VehicleEntityBuilder()
      .withStartLocationUuid(location.uuid)
      .withBranchUuid(branch.uuid)
      .withPermitBranchUuid(branch.uuid)
      .withEndLocationUuid(location.uuid)
      .withDepotLocationUuid(location.uuid)
      .withDeactivatedAt(new Date())
      .build()

    await dataSource.manager.save(Location, location)
    await dataSource.manager.save(Branch, branch)
    await dataSource.manager.save(Vehicle, vehicle)
  })

  after(async () => await setup.teardown())

  describe('Reactivate vehicle', () => {
    it('should return 401 when not authenticated', async () => {
      const response = await request(setup.httpServer)
        .post(`/vehicles/${vehicle.uuid}/reactivate`)

      expect(response).toHaveStatus(401)
    })

    it('should return 403 when not authorized', async () => {
      const response = await request(setup.httpServer)
        .post(`/vehicles/${vehicle.uuid}/reactivate`)
        .set('Authorization', `Bearer ${defaultUser.token}`)

      expect(response).toHaveStatus(403)
    })

    it('should reactivate vehicle', async () => {
      const response = await request(setup.httpServer)
        .post(`/vehicles/${vehicle.uuid}/reactivate`)
        .set('Authorization', `Bearer ${adminUser.token}`)

      expect(response).toHaveStatus(200)
    })
  })
})

import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import dayjs from 'dayjs'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { ReactivateVehicleUseCase } from '../reactivate-vehicle.use-case.js'
import { VehicleReactivatedEvent } from '../vehicle-reactivated.event.js'
import { VehicleNotDeactivatedError } from '../errors/vehicle-not-deactivated.error.js'
import { VehicleEntityBuilder } from '../../../entities/vehicle.entity-builder.js'
import { Vehicle } from '../../../entities/vehicle.entity.js'

describe('ReactivateVehicleUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when reactivating a vehicle', async () => {
    const vehicle = new VehicleEntityBuilder()
      .withDeactivatedAt(dayjs().subtract(1, 'day').toDate())
      .build()

    const repository = createStubInstance(Repository<Vehicle>)
    repository.findOneBy.resolves({ ...vehicle, deactivatedAt: new Date() })
    repository.existsBy.resolves(true)
    repository.update.resolves()

    const eventEmitter = createStubInstance(DomainEventEmitter)

    const useCase = new ReactivateVehicleUseCase(repository, eventEmitter, stubDataSource())

    await expect(useCase.execute(vehicle.uuid)).resolves.toBeUndefined()
    expect(eventEmitter).toHaveEmitted(new VehicleReactivatedEvent(vehicle.uuid))
  })

  it('throws error when vehicle is not deactivated', async () => {
    const vehicle = new VehicleEntityBuilder().build()

    const repository = createStubInstance(Repository<Vehicle>)

    repository.findOneBy.resolves({ ...vehicle, deactivatedAt: new Date() })
    repository.existsBy.resolves(false)

    const eventEmitter = createStubInstance(DomainEventEmitter)

    const useCase = new ReactivateVehicleUseCase(repository, eventEmitter, stubDataSource())

    await expect(useCase.execute(vehicle.uuid))
      .rejects.toThrow(new VehicleNotDeactivatedError())
  })
})

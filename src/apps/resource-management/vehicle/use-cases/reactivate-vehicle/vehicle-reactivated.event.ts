import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { VehicleUuid } from '../../entities/vehicle.uuid.js'

@OneOfMeta(DomainEventLog, DomainEventType.VEHICLE_REACTIVATED)
export class VehicleReactivatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly vehicleUuid: VehicleUuid

  constructor (
    vehicleUuid: VehicleUuid
  ) {
    this.vehicleUuid = vehicleUuid
  }
}

@RegisterDomainEvent(DomainEventType.VEHICLE_REACTIVATED, 1)
export class VehicleReactivatedEvent extends DomainEvent<VehicleReactivatedEventContent> {
  constructor (vehicleUuid: VehicleUuid) {
    super({ content: new VehicleReactivatedEventContent(vehicleUuid) })
  }
}

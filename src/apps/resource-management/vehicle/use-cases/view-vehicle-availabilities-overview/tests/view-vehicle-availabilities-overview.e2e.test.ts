import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { stringify } from 'qs'
import { SortDirection } from '@wisemen/pagination'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { BranchEntityBuilder } from '../../../../branch/builders/branch.entity.builder.js'
import { Branch } from '../../../../branch/branch.entity.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { TypesenseCollectionName } from '../../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollectionService } from '../../../../../../modules/typesense/services/typesense-collection.service.js'
import { MigrateCollectionsUseCase } from '../../../../../../modules/typesense/use-cases/migrate-collections/migrate-collections.use-case.js'
import { VehicleEntityBuilder } from '../../../../vehicle/entities/vehicle.entity-builder.js'
import { Vehicle } from '../../../../vehicle/entities/vehicle.entity.js'
import { VehicleUnavailability } from '../../../../vehicle-unavailability/entities/vehicle-unavailability.entity.js'
import { VehicleUnavailabilityEntityBuilder } from '../../../../vehicle-unavailability/entities/vechile-unavailability.enitity-builder.js'
import { ViewVehicleAvailabilitiesOverviewQueryBuilder } from '../view-vehicle-availabilities-overview.query-builder.js'
import { ViewVehicleAvailabilitiesOverviewResponse, ViewvehicleAvailabilitiesOverviewResponseItem } from '../view-vehicle-availabilities-overview.response.js'
import { VehicleUnavailabilityReason } from '../../../enums/vehicle-unavailability-reason.enum.js'
import { VehicleAvailabilityTimeSlotType } from '../vehicle-availability-slot-type.js'
import { ViewVehiclesAvailabilitiesSortQueryKey } from '../query/view-vehicle-availabilities-sort-query-key.enum.js'

describe('Get vehicles availabililties overview e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  let vehicleA: Vehicle
  let vehicleB: Vehicle
  let vehicleC: Vehicle

  let vehicleUnavailability: VehicleUnavailability

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    const branch = new BranchEntityBuilder().build()
    const location = new LocationBuilder().build()

    vehicleA = new VehicleEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withLicensePlate('1-ABC-123')
      .withPermitBranchUuid(branch.uuid)
      .withDepotLocationUuid(location.uuid)
      .withStartLocationUuid(location.uuid)
      .withEndLocationUuid(location.uuid)
      .build()

    vehicleB = new VehicleEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withLicensePlate('2-DEF-456')
      .withPermitBranchUuid(branch.uuid)
      .withDepotLocationUuid(location.uuid)
      .withStartLocationUuid(location.uuid)
      .withEndLocationUuid(location.uuid)
      .build()

    vehicleC = new VehicleEntityBuilder()
      .withBranchUuid(branch.uuid)
      .withLicensePlate('3-GHI-789')
      .withPermitBranchUuid(branch.uuid)
      .withDepotLocationUuid(location.uuid)
      .withStartLocationUuid(location.uuid)
      .withEndLocationUuid(location.uuid)
      .build()

    await setup.dataSource.manager.insert(Branch, branch)
    await setup.dataSource.manager.insert(Location, location)
    await setup.dataSource.manager.insert(Vehicle, [vehicleA, vehicleC, vehicleB])

    const typesenseMigrator = setup.testModule.get(MigrateCollectionsUseCase, { strict: false })
    const typesenseCollectionService = setup.testModule.get(TypesenseCollectionService)

    await typesenseMigrator.execute(true, [TypesenseCollectionName.VEHICLE])
    await typesenseCollectionService
      .importManually(TypesenseCollectionName.VEHICLE, [vehicleA, vehicleC, vehicleB])

    vehicleUnavailability = new VehicleUnavailabilityEntityBuilder()
      .withVehicleUuid(vehicleA.uuid)
      .withFrom(new Date('2025-06-02T08:00:00.000Z'))
      .withUntil(new Date('2025-06-04T10:00:00.000Z'))
      .withReason(VehicleUnavailabilityReason.MAINTENANCE)
      .withRemark('Vehicle is under maintenance')
      .build()

    await setup.dataSource.manager.insert(VehicleUnavailability, vehicleUnavailability)
  })

  after(async () => await setup.teardown())

  describe('Vehicle Availabilities generation', () => {
    it('returns vehicle availabilities in a paginated format', async () => {
      const query = new ViewVehicleAvailabilitiesOverviewQueryBuilder()
        .withFrom('2025-06-01')
        .withUntil('2025-06-07')
        .build()

      const response = await request(setup.httpServer)
        .get('/vehicles-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)
      expect(response.body.items[0].vehicle.uuid).toBe(vehicleA.uuid)

      const body = response.body as ViewVehicleAvailabilitiesOverviewResponse

      const vehicleResponse = body.items.find(
        (item: ViewvehicleAvailabilitiesOverviewResponseItem) =>
          item.vehicle.uuid === vehicleA.uuid
      )

      expect(vehicleResponse).toBeDefined()
      expect(vehicleResponse).toEqual(
        {
          vehicle: {
            uuid: vehicleA.uuid,
            licensePlate: vehicleA.licensePlate,
            capacity: vehicleA.capacity,
            deactivatedAt: null
          },
          availabilities: [
            {
              date: '2025-06-01',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
                  start: '2025-06-01T05:00:00.000Z',
                  end: '2025-06-01T20:00:00.000Z',
                  uuid: null,
                  reason: null,
                  remark: null
                }
              ]
            },
            {
              date: '2025-06-02',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
                  start: '2025-06-02T05:00:00.000Z',
                  end: '2025-06-02T08:00:00.000Z',
                  uuid: null,
                  reason: null,
                  remark: null
                },
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_UNAVAILABLE,
                  start: '2025-06-02T08:00:00.000Z',
                  end: '2025-06-02T20:00:00.000Z',
                  uuid: vehicleUnavailability.uuid,
                  reason: VehicleUnavailabilityReason.MAINTENANCE,
                  remark: null
                }
              ]
            },
            {
              date: '2025-06-03',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_UNAVAILABLE,
                  start: '2025-06-03T05:00:00.000Z',
                  end: '2025-06-03T20:00:00.000Z',
                  uuid: vehicleUnavailability.uuid,
                  reason: VehicleUnavailabilityReason.MAINTENANCE,
                  remark: null
                }
              ]
            },
            {
              date: '2025-06-04',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_UNAVAILABLE,
                  start: '2025-06-04T05:00:00.000Z',
                  end: '2025-06-04T10:00:00.000Z',
                  uuid: vehicleUnavailability.uuid,
                  reason: VehicleUnavailabilityReason.MAINTENANCE,
                  remark: null
                },
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
                  start: '2025-06-04T10:00:00.000Z',
                  end: '2025-06-04T20:00:00.000Z',
                  uuid: null,
                  reason: null,
                  remark: null
                }
              ]
            },
            {
              date: '2025-06-05',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
                  start: '2025-06-05T05:00:00.000Z',
                  end: '2025-06-05T20:00:00.000Z',
                  uuid: null,
                  reason: null,
                  remark: null
                }
              ]
            },
            {
              date: '2025-06-06',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
                  start: '2025-06-06T05:00:00.000Z',
                  end: '2025-06-06T20:00:00.000Z',
                  uuid: null,
                  reason: null,
                  remark: null
                }
              ]
            },
            {
              date: '2025-06-07',
              slots: [
                {
                  type: VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
                  start: '2025-06-07T05:00:00.000Z',
                  end: '2025-06-07T20:00:00.000Z',
                  uuid: null,
                  reason: null,
                  remark: null
                }
              ]
            }
          ]
        }
      )
    })
  })

  describe('Vehicle Availabilities query', () => {
    it('allows sorting on licensePlate ASC', async () => {
      const query = new ViewVehicleAvailabilitiesOverviewQueryBuilder()
        .withSort(ViewVehiclesAvailabilitiesSortQueryKey.LICENSE_PLATE, SortDirection.ASC)
        .build()

      const response = await request(setup.httpServer)
        .get('/vehicles-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)
      expect(response.body.items[0].vehicle.uuid).toBe(vehicleA.uuid)
      expect(response.body.items[1].vehicle.uuid).toBe(vehicleB.uuid)
      expect(response.body.items[2].vehicle.uuid).toBe(vehicleC.uuid)
    })

    it('allows sorting on licensePlate DESC', async () => {
      const query = new ViewVehicleAvailabilitiesOverviewQueryBuilder()
        .withSort(ViewVehiclesAvailabilitiesSortQueryKey.LICENSE_PLATE, SortDirection.DESC)
        .build()

      const response = await request(setup.httpServer)
        .get('/vehicles-availabilities-overview')
        .set('Authorization', `Bearer ${adminUser.token}`)
        .query(stringify(query))

      expect(response).toHaveStatus(HttpStatus.OK)
      expect(response.body.items[0].vehicle.uuid).toBe(vehicleC.uuid)
      expect(response.body.items[1].vehicle.uuid).toBe(vehicleB.uuid)
      expect(response.body.items[2].vehicle.uuid).toBe(vehicleA.uuid)
    })
  })
})

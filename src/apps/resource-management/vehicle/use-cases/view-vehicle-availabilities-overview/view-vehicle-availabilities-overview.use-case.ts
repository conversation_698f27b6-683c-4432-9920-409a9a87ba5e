import { Injectable } from '@nestjs/common'
import { SearchParams } from 'typesense/lib/Typesense/Documents.js'
import dayjs, { Dayjs } from 'dayjs'
import { Any, LessThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { SortDirection } from '@wisemen/pagination'
import { TypesenseQueryService } from '../../../../../modules/typesense/services/typesense-query.service.js'
import { VehicleTypesenseCollection } from '../../typesense/vehicle.collection.js'
import { TypesenseOperationMode } from '../../../../../modules/typesense/param-builders/enums/typesense-operation-mode.enum.js'
import { TypesenseSearchParamsBuilder } from '../../../../../modules/typesense/param-builders/search-params.builder.js'
import { TypesenseCollectionName } from '../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { VehicleUnavailability } from '../../../vehicle-unavailability/entities/vehicle-unavailability.entity.js'
import { VehicleUuid } from '../../entities/vehicle.uuid.js'
import { VehicleAvailabilityCombiner } from '../../utils/vehicle-availability-combiner.js'
import { Vehicle } from '../../entities/vehicle.entity.js'
import { orderItems } from '../../../../../utils/ordering/order-items.js'
import { VehiclePeriodAvailability } from './vehicle-period-availability.js'
import { ViewVehicleAvailabilitiesOverviewQuery } from './query/view-vehicle-availabilities-overview.query.js'
import { ViewVehicleAvailabilitiesOverviewResponse } from './view-vehicle-availabilities-overview.response.js'
import { VehicleDayAvailability } from './vehicle-day-availability.js'

@Injectable()
export class ViewVehicleAvailabilitiesOverviewUseCase {
  constructor (
    private readonly typesense: TypesenseQueryService,
    @InjectRepository(VehicleUnavailability)
    private readonly vehicleUnavailabilityRepository: Repository<VehicleUnavailability>,
    @InjectRepository(Vehicle)
    private readonly vehicleRepository: Repository<Vehicle>
  ) {}

  public async execute (
    query: ViewVehicleAvailabilitiesOverviewQuery
  ): Promise<ViewVehicleAvailabilitiesOverviewResponse> {
    const searchParams = this.buildTypesenseSearchParams(query)
    const vehicles = await this.typesense.search(TypesenseCollectionName.VEHICLE, searchParams)

    const from = dayjs(query.filter.from)
    const until = dayjs(query.filter.until)

    const vehicleUuids = vehicles.items.map(vehicle => vehicle.id)
    const vehiclesWithMetadata = await this.getVehiclesWithMetadata(vehicleUuids)

    if (vehicleUuids.length === 0) {
      return new ViewVehicleAvailabilitiesOverviewResponse([], vehicles.meta)
    }

    const orderedVechiles
    = orderItems(vehiclesWithMetadata, vehicleUuids, 'uuid')

    const vehicleUnavailabilities = await this.getVehicleUnavailabilities(
      from,
      until,
      vehicleUuids
    )

    const vehiclePeriods: VehiclePeriodAvailability[] = []
    for (const vehicle of orderedVechiles) {
      const mappedVehicleUnavailabilities = vehicleUnavailabilities
        .filter(unavailability => unavailability.vehicleUuid === vehicle.uuid)

      const dayAvailabilities = this.getVehicleDayAvailabilities(
        from,
        until,
        mappedVehicleUnavailabilities,
        vehicle.deactivatedAt ? dayjs(vehicle.deactivatedAt) : null
      )

      vehiclePeriods.push(new VehiclePeriodAvailability(vehicle, dayAvailabilities))
    }

    return new ViewVehicleAvailabilitiesOverviewResponse(
      vehiclePeriods,
      vehicles.meta
    )
  }

  private getVehicleDayAvailabilities (
    from: Dayjs,
    until: Dayjs,
    driverVehicleUnavailabilities: VehicleUnavailability[],
    deactivatedAt: Dayjs | null
  ): VehicleDayAvailability[] {
    if (deactivatedAt && deactivatedAt.isBefore(from)) {
      return []
    }

    const effectiveUntil = (deactivatedAt && deactivatedAt.isBefore(until))
      ? deactivatedAt
      : until

    const periodAvailabilities = new VehicleAvailabilityCombiner(
      from,
      effectiveUntil,
      driverVehicleUnavailabilities
    ).getByDay()

    return Array.from(periodAvailabilities.entries()).map(([day, slots]) => {
      return new VehicleDayAvailability(day, slots)
    })
  }

  private buildTypesenseSearchParams (
    query: ViewVehicleAvailabilitiesOverviewQuery
  ): SearchParams {
    const searchParamsBuilder = new TypesenseSearchParamsBuilder<VehicleTypesenseCollection>()
      .withQuery(query.search)
      .addSearchOn('licensePlate', TypesenseOperationMode.ALWAYS)
      .withOffset(query.pagination?.offset)
      .withLimit(query.pagination?.limit)

    if (query.sort !== undefined) {
      for (const sort of query.sort ?? []) {
        searchParamsBuilder.addSortOn(sort.key, sort.order)
      }
    } else {
      searchParamsBuilder.addSortOn('licensePlate', SortDirection.ASC)
    }

    return searchParamsBuilder.build()
  }

  private getVehicleUnavailabilities (
    from: Dayjs,
    until: Dayjs,
    vehicleUuids: VehicleUuid[]
  ): Promise<VehicleUnavailability[]> {
    return this.vehicleUnavailabilityRepository.find({
      where: {
        vehicleUuid: Any(vehicleUuids),
        from: LessThanOrEqual(until.toDate()),
        until: MoreThanOrEqual(from.toDate())
      }
    })
  }

  private async getVehiclesWithMetadata (
    vehicleUuids: VehicleUuid[]
  ): Promise<Vehicle[]> {
    return this.vehicleRepository.find({
      where: { uuid: Any(vehicleUuids) }
    })
  }
}

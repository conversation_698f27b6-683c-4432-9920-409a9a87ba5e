import { Dayjs } from 'dayjs'
import { VehicleAvailabilityTimeSlotType } from '../../use-cases/view-vehicle-availabilities-overview/vehicle-availability-slot-type.js'
import { VehicleAvailabilityTimeSlot } from '../../use-cases/view-vehicle-availabilities-overview/vehicle-availability-time-slot.js'

export class VehicleAvailabilityMapper {
  private from: Dayjs
  private readonly to: Dayjs
  private generatedAvailabities: VehicleAvailabilityTimeSlot[]

  constructor (
    from: Dayjs,
    to: Dayjs
  ) {
    this.from = from
    this.to = to
    this.generatedAvailabities = []
  }

  generate (): VehicleAvailabilityTimeSlot[] {
    while (!this.from.isAfter(this.to)) {
      const currentDate = this.from

      this.generatedAvailabities.push(new VehicleAvailabilityTimeSlot(
        null,
        VehicleAvailabilityTimeSlotType.VEHICLE_AVAILABLE,
        currentDate.hour(5).minute(0).toDate(),
        currentDate.hour(20).minute(0).toDate(),
        null,
        null
      ))

      this.from = this.from.add(1, 'day')
    }

    return this.generatedAvailabities
  }
}

import { Module } from '@nestjs/common'
import { CreateVehicleModule } from './use-cases/create-vehicle/create-vehicle.module.js'
import { UpdateVehicleModule } from './use-cases/update-vehicle/update-vehicle.module.js'
import { ViewVehicleDetailModule } from './use-cases/view-vehicle-detail/view-vehicle-detail.module.js'
import { DeactivateVehicleModule } from './use-cases/deactivate-vehicle/deactivate-vehicle.module.js'
import { GetVehiclesModule } from './use-cases/get-vehicles/get-vehicles.module.js'
import { ViewVehicleAvailabilitiesOverviewModule } from './use-cases/view-vehicle-availabilities-overview/view-vehicle-availabilities-overview.module.js'
import { ReactivateVehicleModule } from './use-cases/reactivate-vehicle/reactivate-vehicle.module.js'

@Module({
  imports: [
    CreateVehicleModule,
    DeactivateVehicleModule,
    ReactivateVehicleModule,
    UpdateVehicleModule,
    ViewVehicleDetailModule,
    GetVehiclesModule,
    ViewVehicleAvailabilitiesOverviewModule
  ]
})
export class VehicleModule { }

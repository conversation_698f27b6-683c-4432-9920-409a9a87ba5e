export enum DomainEventType {
  ABSENCE_CREATED = 'absence.created',

  ACCEPTED_TRANSPORT_ORDER_CREATED = 'accepted-transport-order.created',
  ACCEPTED_TRANSPORT_ORDER_UPDATED = 'accepted-transport-order.updated',
  ACCEPTED_TRANSPORT_ORDER_CANCELLED = 'accepted-transport-order.cancelled',

  BASE_CHARGE_CREATED = 'base-charge.created',
  BASE_CHARGE_DELETED = 'base-charge.deleted',
  BASE_CHARGE_UPDATED = 'base-charge.updated',

  CARE_USER_CREATED = 'care-user.created',
  CARE_USER_UPDATED = 'care-user.updated',
  CARE_USER_INFORMATION_UPDATED = 'care-user.information.updated',
  CARE_USER_ADDITIONAL_BILLING_INFORMATION_UPDATED = 'care-user.additional-billing-information.updated',

  CARE_USER_ADDRESS_CREATED = 'care-user-address.created',

  CONTRACT_TYPE_CREATED = 'contract-type.created',
  CONTRACT_TYPE_UPDATED = 'contract-type.updated',

  DEFAULT_CONTRACT_ASSIGNED = 'default-contract.assigned',

  DRIVER_CREATED = 'driver.created',
  DRIVER_UPDATED = 'driver.updated',
  DRIVER_DEACTIVATED = 'driver.deactivated',
  DRIVER_REACTIVATED = 'driver.reactivated',

  FILE_CREATED = 'file.created',
  FILE_DELETED = 'file.deleted',
  FILE_UPLOADED = 'file.uploaded',

  FLEXIBILITY_FACTOR_CREATED = 'flexibility-factor.created',
  FLEXIBILITY_FACTOR_UPDATED = 'flexibility-factor.updated',
  FLEXIBILITY_FACTOR_DELETED = 'flexibility-factor.deleted',

  FULL_DAY_ABSENCE_CREATED = 'full-day-absence.created',
  FULL_DAY_ABSENCE_DELETED = 'full-day-absence.deleted',
  FULL_DAY_ABSENCE_UPDATED = 'full-day-absence.updated',

  HOLDING_CREATED = 'holding.created',
  HOLDING_LINKED_TO_ORGANIZATION = 'holding.linked-to.organization',

  KILOMETER_RATE_CREATED = 'kilometer-rate.created',
  KILOMETER_RATE_DELETED = 'kilometer-rate.deleted',
  KILOMETER_RATE_UPDATED = 'kilometer-rate.updated',

  LOCATION_CREATED = 'location.created',
  LOCATION_UPDATED = 'location.updated',

  MINUTE_RATE_CREATED = 'minute-rate.created',
  MINUTE_RATE_DELETED = 'minute-rate.deleted',
  MINUTE_RATE_UPDATED = 'minute-rate.updated',

  NOTIFICATION_CREATED = 'notification.created',
  NOTIFICATION_READ = 'notification.read',
  NOTIFICATION_READ_ALL = 'notification.read.all',
  NOTIFICATION_UNREAD = 'notification.unread',
  NOTIFICATION_TYPES_MIGRATED = 'notification.types.migrated',
  NOTIFICATION_PREFERENCE_PRESET_UPDATED = 'notification.preference.preset.updated',

  ORDER_REQUEST_CREATED = 'order-request.created',
  ORDER_REQUEST_UPDATED = 'order-request.updated',

  REQUESTED_TRANSPORT_ORDER_CREATED = 'requested-transport-order.created',
  REQUESTED_TRANSPORT_ORDER_UPDATED = 'requested-transport-order.updated',
  REQUESTED_TRANSPORT_ORDER_DELETED = 'requested-transport-order.deleted',

  ORGANIZATION_CREATED = 'organization.created',
  ORGANIZATION_UPDATED = 'organization.updated',
  ORGANIZATION_INFORMATION_UPDATED = 'organization.information.updated',
  ORGANIZATION_ADDITIONAL_BILLING_INFORMATION_UPDATED = 'organization.additional-billing-information.updated',

  PRICING_PARAMETERS_CREATED = 'pricing-parameters.created',
  PRICING_PARAMETERS_UPDATED = 'pricing-parameters.updated',
  PRICING_PARAMETERS_DELETED = 'pricing-parameters.deleted',

  REGION_CORRECTION_CREATED = 'region-correction.created',
  REGION_CORRECTION_UPDATED = 'region-correction.updated',
  REGION_CORRECTION_DELETED = 'region-correction.deleted',

  ROLE_CREATED = 'role.created',
  ROLE_DELETED = 'role.deleted',
  ROLE_RENAMED = 'role.renamed',
  ROLE_PERMISSIONS_UPDATED = 'role.permissions.updated',
  ROLE_PERMISSIONS_CACHE_CLEARED = 'role.permissions.cache.cleared',

  TEST_NOTIFICATION_SENT = 'test-notification.sent',

  SHIFT_CREATED = 'shift.created',
  SHIFT_DELETED = 'shift.deleted',
  SHIFT_HIDDEN = 'shift.hidden',
  SHIFT_REVEALED = 'shift.revealed',
  SHIFT_UPDATED = 'shift.updated',

  SIMULATION_CREATED = 'simulation.created',
  SIMULATION_FAILED = 'simulation.failed',
  SIMULATION_UPDATED = 'simulation.updated',

  VIRTUAL_SEGMENT_CREATED = 'virtual-segment.created',
  VIRTUAL_SEGMENT_UPDATED = 'virtual-segment.updated',

  WEEKEND_MARKUP_CREATED = 'weekend-markup.created',
  WEEKEND_MARKUP_DELETED = 'weekend-markup.deleted',
  WEEKEND_MARKUP_UPDATED = 'weekend-markup.updated',

  UNLOADED_KILOMETER_FACTOR_CREATED = 'unloaded-kilometer-factor.created',
  UNLOADED_KILOMETER_FACTOR_UPDATED = 'unloaded-kilometer-factor.updated',
  UNLOADED_KILOMETER_FACTOR_DELETED = 'unloaded-kilometer-factor.deleted',

  UNLOADED_MINUTE_FACTOR_CREATED = 'unloaded-minute-factor.created',
  UNLOADED_MINUTE_FACTOR_UPDATED = 'unloaded-minute-factor.updated',
  UNLOADED_MINUTE_FACTOR_DELETED = 'unloaded-minute-factor.deleted',

  USER_CREATED = 'user.created',
  USER_ROLE_ASSIGNED = 'user.role-assigned',
  USER_NOTIFICATION_CREATED = 'user.notification.created',
  USER_DEFAULT_NOTIFICATION_PREFERENCES_ASSIGNED = 'user.default-notification-preferences.assigned',

  HOLIDAY_CREATED = 'holiday.created',
  HOLIDAY_UPDATED = 'holiday.updated',
  HOLIDAY_DELETED = 'holiday.deleted',

  VEHICLE_CREATED = 'vehicle.created',
  VEHICLE_DEACTIVATED = 'vehicle.deactivated',
  VEHICLE_REACTIVATED = 'vehicle.reactivated',
  VEHICLE_UPDATED = 'vehicle.updated',

  VEHICLE_UNAVAILABILITY_CREATED = 'vehicle.unavailability.created',
  VEHICLE_UNAVAILABILITY_DELETED = 'vehicle.unavailability.deleted',
  VEHICLE_UNAVAILABILITY_UPDATED = 'vehicle.unavailability.updated',

  DRIVER_AVAILABILITY_CREATED = 'driver.availability.created',
  DRIVER_AVAILABILITY_UPDATED = 'driver.availability.updated',
  DRIVER_AVAILABILITY_DELETED = 'driver.availability.deleted',

  DRIVER_UNAVAILABILITY_CREATED = 'driver.unavailability.created',
  DRIVER_UNAVAILABILITY_UPDATED = 'driver.unavailability.updated',
  DRIVER_UNAVAILABILITY_DELETED = 'driver.unavailability.deleted'
}

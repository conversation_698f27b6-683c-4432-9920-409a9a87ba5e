import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddBookingEntity1753776914108 implements MigrationInterface {
  name = 'AddBookingEntity1753776914108'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."booking_status_enum" AS ENUM('active', 'cancelled', 'completed')`)
    await queryRunner.query(`CREATE TABLE "booking" ("uuid" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "client_uuid" uuid NOT NULL, "client_type" "public"."client_type" NOT NULL, "status" "public"."booking_status_enum" NOT NULL, "is_recurring" boolean NOT NULL DEFAULT false, "start_date" date, "end_date" date, "iteration_interval_weeks" integer, "remarks_for_driver" character varying, "remarks_for_planner" character varying, "contract_uuid" uuid NOT NULL, "care_user_uuid" uuid NOT NULL, "created_by_user_uuid" uuid NOT NULL, "updated_by_user_uuid" uuid NOT NULL, CONSTRAINT "PK_a301e30d2ae38b568400128440a" PRIMARY KEY ("uuid"))`)
    await queryRunner.query(`CREATE INDEX "IDX_e3cc0c008e9a70922a8f04a364" ON "booking" ("updated_at") `)
    await queryRunner.query(`ALTER TABLE "order_request" ADD "accepted_by_user_uuid" uuid`)
    await queryRunner.query(`ALTER TABLE "order_request" ADD CONSTRAINT "FK_a534bb8a2e9d95dd24071f45b2b" FOREIGN KEY ("accepted_by_user_uuid") REFERENCES "user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "booking" ADD CONSTRAINT "FK_a2e0038e8396dbd20646dc9a07d" FOREIGN KEY ("contract_uuid") REFERENCES "contract"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "booking" ADD CONSTRAINT "FK_cb820b226652639242a5ff16052" FOREIGN KEY ("care_user_uuid") REFERENCES "care_user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "booking" ADD CONSTRAINT "FK_8f554712f7e6b869d5fab0cd769" FOREIGN KEY ("created_by_user_uuid") REFERENCES "user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    await queryRunner.query(`ALTER TABLE "booking" ADD CONSTRAINT "FK_e406d36cab170a725233444c329" FOREIGN KEY ("updated_by_user_uuid") REFERENCES "user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "booking" DROP CONSTRAINT "FK_e406d36cab170a725233444c329"`)
    await queryRunner.query(`ALTER TABLE "booking" DROP CONSTRAINT "FK_8f554712f7e6b869d5fab0cd769"`)
    await queryRunner.query(`ALTER TABLE "booking" DROP CONSTRAINT "FK_cb820b226652639242a5ff16052"`)
    await queryRunner.query(`ALTER TABLE "booking" DROP CONSTRAINT "FK_a2e0038e8396dbd20646dc9a07d"`)
    await queryRunner.query(`ALTER TABLE "order_request" DROP CONSTRAINT "FK_a534bb8a2e9d95dd24071f45b2b"`)
    await queryRunner.query(`ALTER TABLE "order_request" DROP COLUMN "accepted_by_user_uuid"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_e3cc0c008e9a70922a8f04a364"`)
    await queryRunner.query(`DROP TABLE "booking"`)
    await queryRunner.query(`DROP TYPE "public"."booking_status_enum"`)
  }
}
